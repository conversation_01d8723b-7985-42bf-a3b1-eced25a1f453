class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String category;
  final bool isFavorite;
  final bool isNew;
  final int? discountPercentage;
  final List<String> tags;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.category,
    this.isFavorite = false,
    this.isNew = false,
    this.discountPercentage,
    this.tags = const [],
  });

  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? imageUrl,
    double? rating,
    int? reviewCount,
    String? category,
    bool? isFavorite,
    bool? isNew,
    int? discountPercentage,
    List<String>? tags,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      imageUrl: imageUrl ?? this.imageUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      category: category ?? this.category,
      isFavorite: isFavorite ?? this.isFavorite,
      isNew: isNew ?? this.isNew,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      tags: tags ?? this.tags,
    );
  }

  bool get hasDiscount => originalPrice != null && originalPrice! > price;
  
  String get formattedPrice => '\$${price.toStringAsFixed(2)}';
  
  String get formattedOriginalPrice => originalPrice != null 
      ? '\$${originalPrice!.toStringAsFixed(2)}' 
      : '';
}
