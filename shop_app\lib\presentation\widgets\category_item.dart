import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/category.dart';

class CategoryItem extends StatelessWidget {
  final Category category;
  final VoidCallback? onTap;

  const CategoryItem({
    super.key,
    required this.category,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: AppConstants.categoryIconSize,
            height: AppConstants.categoryIconSize,
            decoration: BoxDecoration(
              color: category.backgroundColor,
              shape: BoxShape.circle,
            ),
            child: ClipOval(
              child: CachedNetworkImage(
                imageUrl: category.iconUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: category.backgroundColor,
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: category.backgroundColor,
                  child: Icon(
                    _getCategoryIcon(category.id),
                    color: _getCategoryColor(category.id),
                    size: 32,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          SizedBox(
            width: AppConstants.categoryIconSize,
            child: Text(
              category.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId) {
      case 'clothing':
        return Icons.checkroom;
      case 'electronics':
        return Icons.devices;
      case 'home':
        return Icons.home;
      case 'beauty':
        return Icons.face;
      case 'sports':
        return Icons.sports;
      case 'toys':
        return Icons.toys;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String categoryId) {
    switch (categoryId) {
      case 'clothing':
        return AppColors.categoryClothing;
      case 'electronics':
        return AppColors.categoryElectronics;
      case 'home':
        return AppColors.categoryHome;
      case 'beauty':
        return AppColors.categoryBeauty;
      case 'sports':
        return AppColors.categorySports;
      case 'toys':
        return AppColors.categoryToys;
      default:
        return AppColors.primary;
    }
  }
}
