<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Shop App</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>
tailwind.config={
  darkMode: 'class',
  theme:{
    extend:{
      colors:{
        primary:'#FF4E50',
        secondary:'#FC913A',
        dark: {
          bg: '#121212',
          card: '#1E1E1E',
          text: '#E5E5E5'
        }
      },
      borderRadius:{
        'none':'0px',
        'sm':'4px',
        DEFAULT:'8px',
        'md':'12px',
        'lg':'16px',
        'xl':'20px',
        '2xl':'24px',
        '3xl':'32px',
        'full':'9999px',
        'button':'8px'
      }
    }
  }
}
</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: 'Inter', sans-serif;
max-width: 375px;
margin: 0 auto;
min-height: 100vh;
position: relative;
background-color: #f9f9f9;
transition: background-color 0.3s ease;
}
body.dark {
background-color: theme('colors.dark.bg');
color: theme('colors.dark.text');
}
body.dark .bg-white {
background-color: theme('colors.dark.card');
}
body.dark .text-gray-500 {
color: theme('colors.dark.text');
opacity: 0.7;
}
body.dark .text-gray-600 {
color: theme('colors.dark.text');
opacity: 0.8;
}
body.dark .bg-gray-100 {
background-color: theme('colors.dark.card');
}
body.dark .shadow-sm {
box-shadow: 0 1px 2px 0 rgba(255, 255, 255, 0.05);
}
body.dark .border-primary {
border-color: theme('colors.primary');
color: theme('colors.primary');
}
.product-card:hover {
transform: translateY(-2px);
box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.category-scroll::-webkit-scrollbar {
display: none;
}
.add-to-cart-btn {
transition: all 0.3s ease;
}
.add-to-cart-btn:active {
transform: scale(0.95);
}
.badge {
position: absolute;
top: -5px;
right: -5px;
width: 18px;
height: 18px;
border-radius: 50%;
background-color: #FF4E50;
color: white;
font-size: 10px;
display: flex;
align-items: center;
justify-content: center;
}
</style>
</head>
<body>
<!-- Top Navigation Bar -->
<nav class="bg-white shadow-sm fixed top-0 w-full z-50 px-4 py-3 flex items-center justify-between">
<div class="flex items-center">
<h1 class="text-xl font-['Pacifico'] text-primary">logo</h1>
</div>
<div class="flex items-center space-x-3">
<button id="theme-toggle" class="w-8 h-8 flex items-center justify-center cursor-pointer">
<i class="ri-sun-line ri-lg dark:hidden"></i>
<i class="ri-moon-line ri-lg hidden dark:block"></i>
</button>
<div class="w-8 h-8 flex items-center justify-center cursor-pointer">
<i class="ri-search-line ri-lg"></i>
</div>
<div class="w-8 h-8 flex items-center justify-center cursor-pointer relative">
<i class="ri-heart-line ri-lg"></i>
</div>
<div class="w-8 h-8 flex items-center justify-center cursor-pointer relative">
<i class="ri-shopping-cart-line ri-lg"></i>
<span class="badge">3</span>
</div>
</div>
</nav>
<!-- Main Content -->
<main class="pt-16 pb-20">
<!-- Search Bar -->
<div class="px-4 py-3">
<div class="relative">
<input type="text" placeholder="Search products..." class="w-full py-2.5 pl-10 pr-4 bg-gray-100 rounded-button text-sm focus:outline-none focus:ring-2 focus:ring-primary">
<div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-search-line"></i>
</div>
</div>
</div>
<!-- Banner -->
<div class="px-4 py-3">
<div class="relative rounded-lg overflow-hidden h-40">
<img src="https://readdy.ai/api/search-image?query=modern%20shopping%20app%20banner%2C%20colorful%20summer%20sale%20promotion%2C%20fashion%20and%20electronics%20products%2C%20vibrant%20colors%2C%20professional%20e-commerce%20design%2C%20high%20quality&width=375&height=160&seq=1&orientation=landscape" alt="Summer Sale" class="w-full h-full object-cover">
<div class="absolute inset-0 bg-gradient-to-r from-primary/70 to-transparent flex flex-col justify-center p-6">
<h2 class="text-white text-xl font-bold">Summer Sale</h2>
<p class="text-white text-sm mt-1">Up to 50% off on selected items</p>
<button class="mt-3 bg-white text-primary px-4 py-1.5 rounded-button text-sm font-medium !rounded-button cursor-pointer">Shop Now</button>
</div>
</div>
</div>
<!-- Categories -->
<div class="py-4">
<div class="px-4 flex justify-between items-center mb-3">
<h2 class="text-lg font-semibold">Categories</h2>
<button class="text-primary text-sm font-medium cursor-pointer">View All</button>
</div>
<div class="category-scroll flex space-x-4 overflow-x-auto px-4 pb-2">
<div class="flex flex-col items-center space-y-2 cursor-pointer">
<div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20clothing%20and%20fashion%20items%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=2&orientation=squarish" class="w-full h-full object-cover" alt="Clothing">
</div>
<span class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis">Clothing</span>
</div>
<div class="flex flex-col items-center space-y-2 cursor-pointer">
<div class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20electronic%20devices%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=3&orientation=squarish" class="w-full h-full object-cover" alt="Electronics">
</div>
<span class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis">Electronics</span>
</div>
<div class="flex flex-col items-center space-y-2 cursor-pointer">
<div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20home%20decor%20items%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=4&orientation=squarish" class="w-full h-full object-cover" alt="Home">
</div>
<span class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis">Home</span>
</div>
<div class="flex flex-col items-center space-y-2 cursor-pointer">
<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20beauty%20products%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=5&orientation=squarish" class="w-full h-full object-cover" alt="Beauty">
</div>
<span class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis">Beauty</span>
</div>
<div class="flex flex-col items-center space-y-2 cursor-pointer">
<div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20sports%20equipment%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=6&orientation=squarish" class="w-full h-full object-cover" alt="Sports">
</div>
<span class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis">Sports</span>
</div>
<div class="flex flex-col items-center space-y-2 cursor-pointer">
<div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20toys%20and%20games%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=7&orientation=squarish" class="w-full h-full object-cover" alt="Toys">
</div>
<span class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis">Toys</span>
</div>
</div>
</div>
<!-- Featured Products -->
<div class="py-4">
<div class="px-4 flex justify-between items-center mb-3">
<h2 class="text-lg font-semibold">Featured Products</h2>
<button class="text-primary text-sm font-medium cursor-pointer">View All</button>
</div>
<div class="grid grid-cols-2 gap-4 px-4">
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=modern%20wireless%20headphones%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=8&orientation=portrait" alt="Wireless Headphones" class="w-full h-full object-cover">
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-line text-gray-600"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-half-fill text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">4.5</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Wireless Noise Cancelling Headphones</h3>
<div class="flex justify-between items-center mt-2">
<span class="font-semibold text-sm">$129.99</span>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=smart%20watch%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=9&orientation=portrait" alt="Smart Watch" class="w-full h-full object-cover">
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-line text-gray-600"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-line text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">4.0</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Smart Watch Series 7 - Health Monitor</h3>
<div class="flex justify-between items-center mt-2">
<span class="font-semibold text-sm">$199.99</span>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=minimalist%20desk%20lamp%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=10&orientation=portrait" alt="Desk Lamp" class="w-full h-full object-cover">
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-fill text-primary"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">5.0</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Modern LED Desk Lamp with Wireless Charging</h3>
<div class="flex justify-between items-center mt-2">
<span class="font-semibold text-sm">$59.99</span>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=portable%20bluetooth%20speaker%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=11&orientation=portrait" alt="Bluetooth Speaker" class="w-full h-full object-cover">
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-line text-gray-600"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-half-fill text-yellow-400 text-xs"></i>
<i class="ri-star-line text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">3.5</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Portable Waterproof Bluetooth Speaker</h3>
<div class="flex justify-between items-center mt-2">
<span class="font-semibold text-sm">$49.99</span>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
</div>
</div>
<!-- Best Sellers -->
<div class="py-4">
<div class="px-4 flex justify-between items-center mb-3">
<h2 class="text-lg font-semibold">Best Sellers</h2>
<button class="text-primary text-sm font-medium cursor-pointer">View All</button>
</div>
<div class="grid grid-cols-2 gap-4 px-4">
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=premium%20coffee%20maker%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=12&orientation=portrait" alt="Coffee Maker" class="w-full h-full object-cover">
<div class="absolute top-0 left-0 bg-primary text-white text-xs px-2 py-1">20% OFF</div>
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-line text-gray-600"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-half-fill text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">4.7</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Premium Coffee Maker with Grinder</h3>
<div class="flex justify-between items-center mt-2">
<div>
<span class="font-semibold text-sm">$79.99</span>
<span class="text-xs text-gray-500 line-through ml-1">$99.99</span>
</div>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=air%20purifier%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=13&orientation=portrait" alt="Air Purifier" class="w-full h-full object-cover">
<div class="absolute top-0 left-0 bg-green-500 text-white text-xs px-2 py-1">NEW</div>
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-line text-gray-600"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-line text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">4.2</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Smart Air Purifier with HEPA Filter</h3>
<div class="flex justify-between items-center mt-2">
<span class="font-semibold text-sm">$149.99</span>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=stylish%20backpack%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=14&orientation=portrait" alt="Backpack" class="w-full h-full object-cover">
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-fill text-primary"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-line text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">4.1</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Anti-Theft Travel Backpack with USB Charging</h3>
<div class="flex justify-between items-center mt-2">
<span class="font-semibold text-sm">$69.99</span>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer">
<div class="relative h-36 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=fitness%20tracker%20watch%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=15&orientation=portrait" alt="Fitness Tracker" class="w-full h-full object-cover">
<div class="absolute top-0 left-0 bg-primary text-white text-xs px-2 py-1">15% OFF</div>
<button class="absolute top-2 right-2 w-7 h-7 bg-white/80 rounded-full flex items-center justify-center">
<i class="ri-heart-line text-gray-600"></i>
</button>
</div>
<div class="p-3">
<div class="flex items-center mb-1">
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-fill text-yellow-400 text-xs"></i>
<i class="ri-star-half-fill text-yellow-400 text-xs"></i>
<span class="text-xs text-gray-500 ml-1">4.6</span>
</div>
<h3 class="text-sm font-medium line-clamp-2">Fitness Tracker with Heart Rate Monitor</h3>
<div class="flex justify-between items-center mt-2">
<div>
<span class="font-semibold text-sm">$42.49</span>
<span class="text-xs text-gray-500 line-through ml-1">$49.99</span>
</div>
<button class="add-to-cart-btn w-7 h-7 bg-primary rounded-full flex items-center justify-center !rounded-full">
<i class="ri-add-line text-white"></i>
</button>
</div>
</div>
</div>
</div>
</div>
<!-- Recently Viewed -->
<div class="py-4">
<div class="px-4 flex justify-between items-center mb-3">
<h2 class="text-lg font-semibold">Recently Viewed</h2>
<button class="text-primary text-sm font-medium cursor-pointer">Clear All</button>
</div>
<div class="flex space-x-4 overflow-x-auto px-4 pb-2">
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer min-w-[140px] max-w-[140px]">
<div class="relative h-28 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=wireless%20earbuds%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=16&orientation=portrait" alt="Wireless Earbuds" class="w-full h-full object-cover">
</div>
<div class="p-2">
<h3 class="text-xs font-medium line-clamp-2">Wireless Earbuds with Charging Case</h3>
<div class="flex justify-between items-center mt-1">
<span class="font-semibold text-xs">$39.99</span>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer min-w-[140px] max-w-[140px]">
<div class="relative h-28 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=smartphone%20stand%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=17&orientation=portrait" alt="Phone Stand" class="w-full h-full object-cover">
</div>
<div class="p-2">
<h3 class="text-xs font-medium line-clamp-2">Adjustable Phone Stand for Desk</h3>
<div class="flex justify-between items-center mt-1">
<span class="font-semibold text-xs">$12.99</span>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer min-w-[140px] max-w-[140px]">
<div class="relative h-28 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=water%20bottle%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=18&orientation=portrait" alt="Water Bottle" class="w-full h-full object-cover">
</div>
<div class="p-2">
<h3 class="text-xs font-medium line-clamp-2">Insulated Stainless Steel Water Bottle</h3>
<div class="flex justify-between items-center mt-1">
<span class="font-semibold text-xs">$24.99</span>
</div>
</div>
</div>
<div class="product-card bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 cursor-pointer min-w-[140px] max-w-[140px]">
<div class="relative h-28 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=wireless%20charger%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=19&orientation=portrait" alt="Wireless Charger" class="w-full h-full object-cover">
</div>
<div class="p-2">
<h3 class="text-xs font-medium line-clamp-2">Fast Wireless Charger Pad</h3>
<div class="flex justify-between items-center mt-1">
<span class="font-semibold text-xs">$29.99</span>
</div>
</div>
</div>
</div>
</div>
</main>
<!-- Bottom Navigation -->
<nav class="bg-white shadow-[0_-2px_10px_rgba(0,0,0,0.05)] fixed bottom-0 w-full z-50">
<div class="grid grid-cols-5 h-16">
<a href="#" class="flex flex-col items-center justify-center cursor-pointer text-primary">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-home-5-fill ri-lg"></i>
</div>
<span class="text-xs mt-1">Home</span>
</a>
<a href="#" class="flex flex-col items-center justify-center cursor-pointer text-gray-500">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-apps-line ri-lg"></i>
</div>
<span class="text-xs mt-1">Categories</span>
</a>
<a href="#" class="flex flex-col items-center justify-center cursor-pointer text-gray-500">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-heart-line ri-lg"></i>
</div>
<span class="text-xs mt-1">Wishlist</span>
</a>
<a href="#" class="flex flex-col items-center justify-center cursor-pointer text-gray-500">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-shopping-cart-line ri-lg"></i>
</div>
<span class="text-xs mt-1">Cart</span>
</a>
<a href="#" class="flex flex-col items-center justify-center cursor-pointer text-gray-500">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-user-line ri-lg"></i>
</div>
<span class="text-xs mt-1">Profile</span>
</a>
</div>
</nav>
<!-- Add to Cart Modal -->
<div id="cart-modal" class="fixed inset-0 z-50 hidden">
<div class="absolute inset-0 bg-black/50" id="modal-backdrop"></div>
<div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl p-4 transform transition-transform duration-300" id="modal-content" style="transform: translateY(100%);">
<div class="flex justify-between items-center mb-4">
<h3 class="text-lg font-semibold">Added to Cart</h3>
<button id="close-modal" class="w-8 h-8 flex items-center justify-center cursor-pointer">
<i class="ri-close-line ri-lg"></i>
</button>
</div>
<div class="flex items-center space-x-4 mb-4">
<div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=wireless%20headphones%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=80&height=80&seq=20&orientation=squarish" alt="Product" class="w-full h-full object-cover">
</div>
<div class="flex-1">
<h4 class="text-sm font-medium">Wireless Noise Cancelling Headphones</h4>
<p class="text-sm text-gray-500 mt-1">Color: Black | Quantity: 1</p>
<p class="text-sm font-semibold mt-1">$129.99</p>
</div>
</div>
<div class="flex space-x-3">
<button class="flex-1 py-2.5 border border-primary text-primary font-medium rounded-button !rounded-button cursor-pointer">Continue Shopping</button>
<button class="flex-1 py-2.5 bg-primary text-white font-medium rounded-button !rounded-button cursor-pointer">View Cart</button>
</div>
</div>
</div>
<!-- Scripts -->
<script id="product-interaction">
document.addEventListener('DOMContentLoaded', function() {
const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
const cartModal = document.getElementById('cart-modal');
const modalBackdrop = document.getElementById('modal-backdrop');
const modalContent = document.getElementById('modal-content');
const closeModal = document.getElementById('close-modal');
const continueShoppingBtn = document.querySelector('button.border-primary');
function showModal() {
cartModal.classList.remove('hidden');
setTimeout(() => {
modalContent.style.transform = 'translateY(0)';
}, 10);
}
function hideModal() {
modalContent.style.transform = 'translateY(100%)';
setTimeout(() => {
cartModal.classList.add('hidden');
}, 300);
}
addToCartButtons.forEach(button => {
button.addEventListener('click', function(e) {
e.preventDefault();
e.stopPropagation();
// Animation
this.classList.add('bg-green-500');
setTimeout(() => {
this.classList.remove('bg-green-500');
this.classList.add('bg-primary');
}, 500);
showModal();
});
});
[closeModal, modalBackdrop, continueShoppingBtn].forEach(el => {
el.addEventListener('click', hideModal);
});
});
</script>
<script id="wishlist-interaction">
document.addEventListener('DOMContentLoaded', function() {
const wishlistButtons = document.querySelectorAll('.ri-heart-line, .ri-heart-fill');
wishlistButtons.forEach(button => {
button.parentElement.addEventListener('click', function(e) {
e.preventDefault();
e.stopPropagation();
if (button.classList.contains('ri-heart-line')) {
button.classList.remove('ri-heart-line');
button.classList.add('ri-heart-fill');
button.classList.add('text-primary');
} else {
button.classList.remove('ri-heart-fill');
button.classList.remove('text-primary');
button.classList.add('ri-heart-line');
button.classList.add('text-gray-600');
}
});
});
});
</script>
<script id="product-card-interaction">
document.addEventListener('DOMContentLoaded', function() {
const productCards = document.querySelectorAll('.product-card');
productCards.forEach(card => {
card.addEventListener('click', function() {
// In a real app, this would navigate to the product detail page
console.log('Navigate to product detail page');
});
});
});
</script>
<script id="theme-toggle-interaction">
document.addEventListener('DOMContentLoaded', function() {
  const themeToggleBtn = document.getElementById('theme-toggle');
  
  // Check for saved theme preference or system preference
  if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
    document.body.classList.add('dark');
  }
  // Toggle theme
  themeToggleBtn.addEventListener('click', function() {
    if (document.body.classList.contains('dark')) {
      document.body.classList.remove('dark');
      localStorage.theme = 'light';
    } else {
      document.body.classList.add('dark');
      localStorage.theme = 'dark';
    }
  });
});
</script>
</body>
</html>