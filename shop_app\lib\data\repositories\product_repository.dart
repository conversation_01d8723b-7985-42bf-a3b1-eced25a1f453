import '../models/product.dart';

class ProductRepository {
  static List<Product> getFeaturedProducts() {
    return [
      Product(
        id: '1',
        name: 'Wireless Noise Cancelling Headphones',
        description: 'Premium wireless headphones with active noise cancellation',
        price: 129.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=modern%20wireless%20headphones%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=8&orientation=portrait',
        rating: 4.5,
        reviewCount: 128,
        category: 'electronics',
        tags: ['wireless', 'noise-cancelling', 'premium'],
      ),
      Product(
        id: '2',
        name: 'Smart Watch Series 7 - Health Monitor',
        description: 'Advanced smartwatch with health monitoring features',
        price: 199.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=smart%20watch%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=9&orientation=portrait',
        rating: 4.0,
        reviewCount: 89,
        category: 'electronics',
        tags: ['smartwatch', 'health', 'fitness'],
      ),
      Product(
        id: '3',
        name: 'Modern LED Desk Lamp with Wireless Charging',
        description: 'Stylish desk lamp with built-in wireless charging pad',
        price: 59.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=minimalist%20desk%20lamp%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=10&orientation=portrait',
        rating: 5.0,
        reviewCount: 45,
        category: 'home',
        isFavorite: true,
        tags: ['lamp', 'wireless-charging', 'modern'],
      ),
      Product(
        id: '4',
        name: 'Portable Waterproof Bluetooth Speaker',
        description: 'Compact speaker with excellent sound quality',
        price: 49.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=portable%20bluetooth%20speaker%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=11&orientation=portrait',
        rating: 3.5,
        reviewCount: 67,
        category: 'electronics',
        tags: ['speaker', 'bluetooth', 'waterproof'],
      ),
    ];
  }

  static List<Product> getBestSellers() {
    return [
      Product(
        id: '5',
        name: 'Premium Coffee Maker with Grinder',
        description: 'Professional coffee maker with built-in grinder',
        price: 79.99,
        originalPrice: 99.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=premium%20coffee%20maker%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=12&orientation=portrait',
        rating: 4.7,
        reviewCount: 234,
        category: 'home',
        discountPercentage: 20,
        tags: ['coffee', 'grinder', 'premium'],
      ),
      Product(
        id: '6',
        name: 'Smart Air Purifier with HEPA Filter',
        description: 'Advanced air purifier with smart controls',
        price: 149.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=air%20purifier%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=13&orientation=portrait',
        rating: 4.2,
        reviewCount: 156,
        category: 'home',
        isNew: true,
        tags: ['air-purifier', 'hepa', 'smart'],
      ),
      Product(
        id: '7',
        name: 'Anti-Theft Travel Backpack with USB Charging',
        description: 'Secure backpack with USB charging port',
        price: 69.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=stylish%20backpack%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=14&orientation=portrait',
        rating: 4.1,
        reviewCount: 89,
        category: 'clothing',
        isFavorite: true,
        tags: ['backpack', 'travel', 'usb-charging'],
      ),
      Product(
        id: '8',
        name: 'Fitness Tracker with Heart Rate Monitor',
        description: 'Comprehensive fitness tracking device',
        price: 42.49,
        originalPrice: 49.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=fitness%20tracker%20watch%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=180&height=144&seq=15&orientation=portrait',
        rating: 4.6,
        reviewCount: 178,
        category: 'sports',
        discountPercentage: 15,
        tags: ['fitness', 'heart-rate', 'tracker'],
      ),
    ];
  }

  static List<Product> getRecentlyViewed() {
    return [
      Product(
        id: '9',
        name: 'Wireless Earbuds with Charging Case',
        description: 'Compact wireless earbuds with long battery life',
        price: 39.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=wireless%20earbuds%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=16&orientation=portrait',
        rating: 4.3,
        reviewCount: 92,
        category: 'electronics',
        tags: ['earbuds', 'wireless', 'compact'],
      ),
      Product(
        id: '10',
        name: 'Adjustable Phone Stand for Desk',
        description: 'Ergonomic phone stand for desk use',
        price: 12.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=smartphone%20stand%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=17&orientation=portrait',
        rating: 4.0,
        reviewCount: 34,
        category: 'electronics',
        tags: ['phone-stand', 'desk', 'adjustable'],
      ),
      Product(
        id: '11',
        name: 'Insulated Stainless Steel Water Bottle',
        description: 'Keep drinks hot or cold for hours',
        price: 24.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=water%20bottle%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=18&orientation=portrait',
        rating: 4.8,
        reviewCount: 156,
        category: 'sports',
        tags: ['water-bottle', 'insulated', 'stainless-steel'],
      ),
      Product(
        id: '12',
        name: 'Fast Wireless Charger Pad',
        description: 'Quick wireless charging for compatible devices',
        price: 29.99,
        imageUrl: 'https://readdy.ai/api/search-image?query=wireless%20charger%2C%20product%20photography%20on%20clean%20white%20background%2C%20high%20quality%2C%20detailed%2C%20professional%20e-commerce%20style&width=140&height=112&seq=19&orientation=portrait',
        rating: 4.4,
        reviewCount: 78,
        category: 'electronics',
        tags: ['wireless-charger', 'fast-charging', 'pad'],
      ),
    ];
  }

  static List<Product> getAllProducts() {
    return [
      ...getFeaturedProducts(),
      ...getBestSellers(),
      ...getRecentlyViewed(),
    ];
  }

  static List<Product> getProductsByCategory(String categoryId) {
    return getAllProducts()
        .where((product) => product.category == categoryId)
        .toList();
  }

  static List<Product> searchProducts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return getAllProducts()
        .where((product) =>
            product.name.toLowerCase().contains(lowercaseQuery) ||
            product.description.toLowerCase().contains(lowercaseQuery) ||
            product.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery)))
        .toList();
  }
}
